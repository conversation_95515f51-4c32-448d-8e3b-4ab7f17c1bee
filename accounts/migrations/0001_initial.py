# Generated by Django 5.2.3 on 2025-06-23 13:36

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="ScrapCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                (
                    "icon",
                    models.CharField(
                        blank=True, help_text="CSS icon class or emoji", max_length=50
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("sort_order", models.PositiveIntegerField(default=0)),
            ],
            options={
                "verbose_name_plural": "Scrap Categories",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="email address"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "user_type",
                    models.CharField(
                        choices=[
                            ("regular", "Regular User"),
                            ("dealer", "Scrap Dealer"),
                            ("admin", "Admin"),
                        ],
                        default="regular",
                        max_length=10,
                    ),
                ),
                (
                    "phone_number",
                    models.CharField(
                        blank=True,
                        max_length=15,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                ("eco_points", models.PositiveIntegerField(default=0)),
                (
                    "profile_picture",
                    models.ImageField(blank=True, null=True, upload_to="profile_pics/"),
                ),
                ("address", models.TextField(blank=True)),
                ("city", models.CharField(blank=True, max_length=100)),
                ("state", models.CharField(blank=True, max_length=100)),
                ("pincode", models.CharField(blank=True, max_length=10)),
                ("is_verified", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "user",
                "verbose_name_plural": "users",
                "abstract": False,
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name="DealerProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("business_name", models.CharField(max_length=200)),
                (
                    "business_registration_number",
                    models.CharField(max_length=50, unique=True),
                ),
                ("gst_number", models.CharField(blank=True, max_length=15)),
                (
                    "business_license",
                    models.ImageField(blank=True, upload_to="dealer_documents/"),
                ),
                ("business_address", models.TextField()),
                ("business_phone", models.CharField(max_length=15)),
                ("business_email", models.EmailField(max_length=254)),
                ("website", models.URLField(blank=True)),
                (
                    "verification_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Verification"),
                            ("verified", "Verified"),
                            ("rejected", "Rejected"),
                            ("suspended", "Suspended"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("verification_date", models.DateTimeField(blank=True, null=True)),
                (
                    "years_in_business",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                (
                    "specialization",
                    models.TextField(
                        help_text="Types of scrap materials you specialize in"
                    ),
                ),
                (
                    "minimum_quantity",
                    models.CharField(
                        blank=True,
                        help_text="Minimum quantity for pickup",
                        max_length=100,
                    ),
                ),
                ("pickup_available", models.BooleanField(default=True)),
                ("delivery_available", models.BooleanField(default=False)),
                (
                    "operating_hours",
                    models.CharField(
                        blank=True, help_text="e.g., Mon-Sat 9AM-6PM", max_length=200
                    ),
                ),
                (
                    "average_rating",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=3),
                ),
                ("total_ratings", models.PositiveIntegerField(default=0)),
                ("total_transactions", models.PositiveIntegerField(default=0)),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=9, null=True
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=9, null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="dealer_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "verified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="verified_dealers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-verification_date", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ScrapMaterial",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "unit",
                    models.CharField(
                        default="kg",
                        help_text="Unit of measurement (kg, ton, piece, etc.)",
                        max_length=20,
                    ),
                ),
                (
                    "quality_grades",
                    models.JSONField(
                        default=list, help_text="List of applicable quality grades"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="materials",
                        to="accounts.scrapcategory",
                    ),
                ),
            ],
            options={
                "ordering": ["category", "name"],
                "unique_together": {("category", "name")},
            },
        ),
        migrations.CreateModel(
            name="DealerInquiry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("subject", models.CharField(max_length=200)),
                ("message", models.TextField()),
                (
                    "quantity",
                    models.CharField(
                        blank=True, help_text="Estimated quantity", max_length=100
                    ),
                ),
                (
                    "contact_preference",
                    models.CharField(
                        choices=[("email", "Email"), ("phone", "Phone")],
                        default="email",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("responded", "Responded"),
                            ("closed", "Closed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("dealer_response", models.TextField(blank=True)),
                ("responded_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "dealer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inquiries",
                        to="accounts.dealerprofile",
                    ),
                ),
                (
                    "material",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.scrapmaterial",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Dealer Inquiries",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DealerRating",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rating",
                    models.PositiveIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ]
                    ),
                ),
                ("review", models.TextField(blank=True)),
                (
                    "transaction_id",
                    models.CharField(
                        blank=True,
                        help_text="Related transaction ID if any",
                        max_length=100,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "dealer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ratings",
                        to="accounts.dealerprofile",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "unique_together": {("dealer", "user")},
            },
        ),
        migrations.CreateModel(
            name="DealerPrice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quality_grade",
                    models.CharField(
                        choices=[
                            ("A", "Grade A (Excellent)"),
                            ("B", "Grade B (Good)"),
                            ("C", "Grade C (Fair)"),
                            ("D", "Grade D (Poor)"),
                        ],
                        max_length=1,
                    ),
                ),
                (
                    "price_per_unit",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "minimum_quantity",
                    models.DecimalField(decimal_places=2, default=1, max_digits=10),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "dealer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prices",
                        to="accounts.dealerprofile",
                    ),
                ),
                (
                    "material",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.scrapmaterial",
                    ),
                ),
            ],
            options={
                "ordering": ["-price_per_unit"],
                "unique_together": {("dealer", "material", "quality_grade")},
            },
        ),
    ]
