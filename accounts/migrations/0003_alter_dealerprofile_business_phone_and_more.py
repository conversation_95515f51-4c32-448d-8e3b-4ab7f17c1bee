# Generated by Django 5.2.3 on 2025-06-24 05:52

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_alter_dealerprofile_business_phone_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='dealerprofile',
            name='business_phone',
            field=models.CharField(help_text='Business phone number (Indian mobile or landline) with or without country code', max_length=15, validators=[django.core.validators.RegexValidator(message='Enter a valid Indian phone number. Examples: +91 **********, 011-********, **********', regex='^(\\+91[-\\s]?|91[-\\s]?|0)?[1-9]\\d{8,9}$')]),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone_number',
            field=models.CharField(blank=True, help_text='Indian phone number (mobile or landline) with or without country code', max_length=15, validators=[django.core.validators.RegexValidator(message='Enter a valid Indian phone number. Examples: +91 **********, 011-********, **********', regex='^(\\+91[-\\s]?|91[-\\s]?|0)?[1-9]\\d{8,9}$')]),
        ),
    ]
