# Generated by Django 5.2.3 on 2025-06-24 05:48

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='dealerprofile',
            name='business_phone',
            field=models.Char<PERSON>ield(help_text='Business phone number (Indian mobile number with or without country code)', max_length=15, validators=[django.core.validators.RegexValidator(message='Enter a valid Indian phone number. Format: +************ or ********** (must start with 6-9)', regex='^(\\+91|91)?[6-9]\\d{9}$')]),
        ),
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='phone_number',
            field=models.CharField(blank=True, help_text='Indian mobile number with or without country code (+91)', max_length=15, validators=[django.core.validators.RegexValidator(message='Enter a valid Indian phone number. Format: +************ or ********** (must start with 6-9)', regex='^(\\+91|91)?[6-9]\\d{9}$')]),
        ),
    ]
