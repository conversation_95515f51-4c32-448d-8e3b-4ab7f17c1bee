# Generated by Django 5.2.3 on 2025-06-24 06:32

import phonenumber_field.modelfields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_alter_dealerprofile_business_phone_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='dealerprofile',
            name='business_phone',
            field=phonenumber_field.modelfields.PhoneNumberField(help_text='Business phone number with country code (e.g., +91 **********)', max_length=128, region='IN'),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone_number',
            field=phonenumber_field.modelfields.PhoneNumberField(blank=True, help_text='Phone number with country code (e.g., +91 **********)', max_length=128, region='IN'),
        ),
    ]
