# Generated by Django 5.2.3 on 2025-06-23 15:05

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReusableItemCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
            ],
            options={
                'verbose_name_plural': 'Reusable Item Categories',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='EcoPointsHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('earned_sale', 'Earned from Sale'), ('earned_purchase', 'Earned from Purchase'), ('earned_review', 'Earned from Review'), ('earned_referral', 'Earned from Referral'), ('spent_discount', 'Spent on Discount'), ('spent_cashout', 'Cashed Out'), ('spent_donation', 'Donated to Charity'), ('bonus', 'Bonus Points'), ('penalty', 'Penalty Deduction')], max_length=20)),
                ('points', models.IntegerField()),
                ('description', models.CharField(max_length=200)),
                ('reference_id', models.CharField(blank=True, help_text='Reference to related transaction/listing', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='eco_points_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Eco Points History',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReusableItemListing',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('brand', models.CharField(blank=True, max_length=100)),
                ('model', models.CharField(blank=True, max_length=100)),
                ('condition', models.CharField(choices=[('like_new', 'Like New'), ('excellent', 'Excellent'), ('good', 'Good'), ('fair', 'Fair'), ('poor', 'Poor')], max_length=20)),
                ('transaction_type', models.CharField(choices=[('sale', 'For Sale'), ('free', 'Free Giveaway'), ('exchange', 'Exchange')], max_length=20)),
                ('price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('exchange_requirements', models.TextField(blank=True, help_text="What you're looking for in exchange")),
                ('pickup_address', models.TextField()),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('pincode', models.CharField(max_length=10)),
                ('image1', models.ImageField(blank=True, null=True, upload_to='reusable_items/')),
                ('image2', models.ImageField(blank=True, null=True, upload_to='reusable_items/')),
                ('image3', models.ImageField(blank=True, null=True, upload_to='reusable_items/')),
                ('image4', models.ImageField(blank=True, null=True, upload_to='reusable_items/')),
                ('ai_condition_grade', models.CharField(blank=True, choices=[('like_new', 'Like New'), ('excellent', 'Excellent'), ('good', 'Good'), ('fair', 'Fair'), ('poor', 'Poor')], max_length=20)),
                ('ai_confidence_score', models.FloatField(default=0.0)),
                ('ai_suggested_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('expired', 'Expired')], default='active', max_length=20)),
                ('views_count', models.PositiveIntegerField(default=0)),
                ('is_featured', models.BooleanField(default=False)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketplace.reusableitemcategory')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reusable_listings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ScrapListing',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quality_grade', models.CharField(choices=[('A', 'Grade A (Excellent)'), ('B', 'Grade B (Good)'), ('C', 'Grade C (Fair)'), ('D', 'Grade D (Poor)')], default='B', max_length=1)),
                ('expected_price', models.DecimalField(decimal_places=2, help_text='Expected price per unit', max_digits=10)),
                ('pickup_address', models.TextField()),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('pincode', models.CharField(max_length=10)),
                ('image1', models.ImageField(blank=True, null=True, upload_to='scrap_listings/')),
                ('image2', models.ImageField(blank=True, null=True, upload_to='scrap_listings/')),
                ('image3', models.ImageField(blank=True, null=True, upload_to='scrap_listings/')),
                ('ai_quality_grade', models.CharField(blank=True, choices=[('A', 'Grade A (Excellent)'), ('B', 'Grade B (Good)'), ('C', 'Grade C (Fair)'), ('D', 'Grade D (Poor)')], max_length=1)),
                ('ai_material_type', models.CharField(blank=True, max_length=100)),
                ('ai_confidence_score', models.FloatField(default=0.0)),
                ('ai_suggested_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('sold', 'Sold'), ('cancelled', 'Cancelled'), ('expired', 'Expired')], default='active', max_length=20)),
                ('views_count', models.PositiveIntegerField(default=0)),
                ('is_featured', models.BooleanField(default=False)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.scrapmaterial')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scrap_listings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ListingInquiry',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('message', models.TextField()),
                ('offered_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('offered_quantity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('responded', 'Responded'), ('accepted', 'Accepted'), ('rejected', 'Rejected'), ('completed', 'Completed')], default='pending', max_length=20)),
                ('seller_response', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('buyer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_inquiries', to=settings.AUTH_USER_MODEL)),
                ('reusable_listing', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='inquiries', to='marketplace.reusableitemlisting')),
                ('scrap_listing', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='inquiries', to='marketplace.scraplisting')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('seller_eco_points', models.PositiveIntegerField(default=0)),
                ('buyer_eco_points', models.PositiveIntegerField(default=0)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('disputed', 'Disputed')], default='pending', max_length=20)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('released', 'Released'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('confirmed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('buyer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchases', to=settings.AUTH_USER_MODEL)),
                ('reusable_listing', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='marketplace.reusableitemlisting')),
                ('scrap_listing', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='marketplace.scraplisting')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
