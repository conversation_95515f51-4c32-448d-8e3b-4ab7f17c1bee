{% extends 'base.html' %}

{% block title %}Price Comparison - AkriOnline{% endblock %}

{% block content %}
<section class="py-20 bg-gradient-to-br from-gray-50 via-white to-emerald-50 min-h-screen">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12 reveal">
            <h1 class="font-display font-bold text-4xl lg:text-5xl text-gray-900 mb-4">
                Price <span class="gradient-primary bg-clip-text text-transparent">Comparison</span>
            </h1>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Compare scrap material prices across verified dealers to get the best deals
            </p>
        </div>

        <!-- Search Form -->
        <div class="glass p-8 rounded-3xl border-2 border-white/20 mb-8">
            <form method="get" class="space-y-6">
                <div class="grid md:grid-cols-3 gap-6">
                    <!-- Category Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category" id="category-select" 
                                class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                            <option value="">Select Category</option>
                            {% for category in categories %}
                                <option value="{{ category.name }}" {% if category.name == selected_category %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Material Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Material</label>
                        <select name="material" id="material-select" 
                                class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                            <option value="">Select Material</option>
                            {% for category in categories %}
                                {% for material in category.materials.all %}
                                    <option value="{{ material.id }}" data-category="{{ category.name }}" 
                                            {% if material.id|stringformat:"s" == selected_material %}selected{% endif %}>
                                        {{ material.name }}
                                    </option>
                                {% endfor %}
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Quality Grade -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quality Grade</label>
                        <select name="grade" 
                                class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                            {% for grade_code, grade_name in grades %}
                                <option value="{{ grade_code }}" {% if grade_code == selected_grade %}selected{% endif %}>
                                    {{ grade_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="text-center">
                    <button type="submit" 
                            class="btn-primary text-white px-8 py-3 rounded-xl font-semibold hover-lift">
                        Compare Prices
                    </button>
                </div>
            </form>
        </div>

        <!-- Results -->
        {% if material and prices %}
            <div class="glass p-8 rounded-3xl border-2 border-white/20">
                <!-- Material Info -->
                <div class="mb-8 text-center">
                    <h2 class="font-display font-bold text-3xl text-gray-900 mb-2">
                        {{ material.name }} - {{ selected_grade|upper }} Grade
                    </h2>
                    <p class="text-gray-600">{{ material.description }}</p>
                    <div class="mt-4 inline-block px-4 py-2 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium">
                        Prices per {{ material.unit }}
                    </div>
                </div>

                <!-- Price Comparison Table -->
                {% if prices %}
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-4 px-4 font-semibold text-gray-900">Dealer</th>
                                    <th class="text-left py-4 px-4 font-semibold text-gray-900">Business Name</th>
                                    <th class="text-center py-4 px-4 font-semibold text-gray-900">Price per {{ material.unit }}</th>
                                    <th class="text-center py-4 px-4 font-semibold text-gray-900">Min Quantity</th>
                                    <th class="text-center py-4 px-4 font-semibold text-gray-900">Rating</th>
                                    <th class="text-center py-4 px-4 font-semibold text-gray-900">Location</th>
                                    <th class="text-center py-4 px-4 font-semibold text-gray-900">Action</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-100">
                                {% for price in prices %}
                                    <tr class="hover:bg-white/50 transition-colors">
                                        <td class="py-4 px-4">
                                            <div class="flex items-center space-x-3">
                                                {% if price.dealer.user.profile_picture %}
                                                    <img src="{{ price.dealer.user.profile_picture.url }}" 
                                                         alt="Profile" class="w-10 h-10 rounded-full object-cover">
                                                {% else %}
                                                    <div class="w-10 h-10 rounded-full gradient-primary flex items-center justify-center">
                                                        <span class="text-white font-bold text-sm">
                                                            {{ price.dealer.business_name|first|upper }}
                                                        </span>
                                                    </div>
                                                {% endif %}
                                                <div>
                                                    <div class="font-medium text-gray-900">{{ price.dealer.user.username }}</div>
                                                    {% if price.dealer.is_verified %}
                                                        <div class="text-xs text-green-600">✓ Verified</div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4">
                                            <div class="font-medium text-gray-900">{{ price.dealer.business_name }}</div>
                                            <div class="text-sm text-gray-600">{{ price.dealer.user.city|default:"Location not specified" }}</div>
                                        </td>
                                        <td class="py-4 px-4 text-center">
                                            <div class="text-2xl font-bold text-emerald-600">₹{{ price.price_per_unit }}</div>
                                        </td>
                                        <td class="py-4 px-4 text-center">
                                            <div class="text-gray-900">{{ price.minimum_quantity }} {{ material.unit }}</div>
                                        </td>
                                        <td class="py-4 px-4 text-center">
                                            <div class="flex items-center justify-center space-x-1">
                                                <span class="text-yellow-400">⭐</span>
                                                <span class="font-medium">{{ price.dealer.average_rating|floatformat:1 }}</span>
                                                <span class="text-gray-500 text-sm">({{ price.dealer.total_ratings }})</span>
                                            </div>
                                        </td>
                                        <td class="py-4 px-4 text-center">
                                            <div class="text-gray-900">{{ price.dealer.user.city|default:"N/A" }}</div>
                                        </td>
                                        <td class="py-4 px-4 text-center">
                                            <a href="{% url 'accounts:dealer_detail' price.dealer.id %}" 
                                               class="bg-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-emerald-700 transition-colors">
                                                Contact
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Price Statistics -->
                    <div class="mt-8 grid md:grid-cols-3 gap-6">
                        <div class="text-center p-4 bg-green-50 rounded-xl">
                            <div class="text-2xl font-bold text-green-600">₹{{ prices.first.price_per_unit }}</div>
                            <div class="text-green-800 font-medium">Highest Price</div>
                        </div>
                        <div class="text-center p-4 bg-blue-50 rounded-xl">
                            <div class="text-2xl font-bold text-blue-600">₹{{ prices.last.price_per_unit }}</div>
                            <div class="text-blue-800 font-medium">Lowest Price</div>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-xl">
                            <div class="text-2xl font-bold text-purple-600">{{ prices|length }}</div>
                            <div class="text-purple-800 font-medium">Total Dealers</div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">💰</div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No prices found</h3>
                        <p class="text-gray-600">No dealers have set prices for this material and grade combination.</p>
                    </div>
                {% endif %}
            </div>
        {% elif selected_material %}
            <div class="glass p-8 rounded-3xl border-2 border-white/20 text-center">
                <div class="text-6xl mb-4">🔍</div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No results found</h3>
                <p class="text-gray-600">Try selecting different search criteria.</p>
            </div>
        {% else %}
            <div class="glass p-8 rounded-3xl border-2 border-white/20 text-center">
                <div class="text-6xl mb-4">📊</div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Select materials to compare prices</h3>
                <p class="text-gray-600">Use the form above to search for specific materials and compare dealer prices.</p>
            </div>
        {% endif %}
    </div>
</section>

<script>
    // Dynamic material filtering based on category
    document.addEventListener('DOMContentLoaded', function() {
        const categorySelect = document.getElementById('category-select');
        const materialSelect = document.getElementById('material-select');
        const allMaterials = Array.from(materialSelect.options);

        function filterMaterials() {
            const selectedCategory = categorySelect.value;
            
            // Clear current options except the first one
            materialSelect.innerHTML = '<option value="">Select Material</option>';
            
            // Add materials for selected category
            allMaterials.slice(1).forEach(option => {
                if (!selectedCategory || option.dataset.category === selectedCategory) {
                    materialSelect.appendChild(option.cloneNode(true));
                }
            });
        }

        categorySelect.addEventListener('change', filterMaterials);
        
        // Initialize on page load
        filterMaterials();
    });
</script>
{% endblock %}
