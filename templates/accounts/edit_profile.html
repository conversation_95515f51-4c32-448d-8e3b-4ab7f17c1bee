{% extends 'base.html' %}

{% block title %}Edit Profile - AkriOnline{% endblock %}

{% block content %}
<section class="py-20 bg-gradient-to-br from-gray-50 via-white to-emerald-50 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12 reveal">
            <h1 class="font-display font-bold text-4xl lg:text-5xl text-gray-900 mb-4">
                Edit <span class="gradient-primary bg-clip-text text-transparent">Profile</span>
            </h1>
            <p class="text-lg text-gray-600">
                Update your account information and preferences
            </p>
        </div>

        <div class="glass p-8 rounded-3xl border-2 border-white/20">
            <form method="post" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                
                <!-- Display Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="p-4 rounded-xl {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Personal Information -->
                <div>
                    <h3 class="font-display font-bold text-xl text-gray-900 mb-4">Personal Information</h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                            <input type="text" id="first_name" name="first_name" 
                                   value="{{ user.first_name }}"
                                   class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                            <input type="text" id="last_name" name="last_name" 
                                   value="{{ user.last_name }}"
                                   class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div>
                    <h3 class="font-display font-bold text-xl text-gray-900 mb-4">Contact Information</h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input type="email" id="email" name="email" 
                                   value="{{ user.email }}"
                                   class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        </div>
                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="tel" id="phone_number" name="phone_number" 
                                   value="{{ user.phone_number }}"
                                   placeholder="+91 9876543210"
                                   class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        </div>
                    </div>
                </div>

                <!-- Location Information -->
                <div>
                    <h3 class="font-display font-bold text-xl text-gray-900 mb-4">Location Information</h3>
                    <div class="space-y-4">
                        <div>
                            <label for="city" class="block text-sm font-medium text-gray-700 mb-2">City</label>
                            <input type="text" id="city" name="city" 
                                   value="{{ user.city }}"
                                   placeholder="Enter your city"
                                   class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        </div>
                        <div>
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                            <textarea id="address" name="address" rows="3"
                                      placeholder="Enter your full address"
                                      class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">{{ user.address }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Profile Picture -->
                <div>
                    <h3 class="font-display font-bold text-xl text-gray-900 mb-4">Profile Picture</h3>
                    <div class="flex items-center space-x-6">
                        {% if user.profile_picture %}
                            <img src="{{ user.profile_picture.url }}" alt="Current Profile Picture" 
                                 class="w-20 h-20 rounded-full object-cover border-2 border-emerald-200">
                        {% else %}
                            <div class="w-20 h-20 rounded-full gradient-primary flex items-center justify-center">
                                <span class="text-2xl font-bold text-white">
                                    {{ user.first_name|first|default:user.username|first|upper }}
                                </span>
                            </div>
                        {% endif %}
                        <div class="flex-1">
                            <input type="file" id="profile_picture" name="profile_picture" 
                                   accept="image/*"
                                   class="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                            <p class="text-sm text-gray-500 mt-2">Upload a new profile picture (JPG, PNG, max 5MB)</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-4 pt-6 border-t border-gray-200">
                    <button type="submit" 
                            class="flex-1 btn-primary text-white py-3 rounded-xl font-semibold hover-lift">
                        Save Changes
                    </button>
                    <a href="{% url 'accounts:profile' %}" 
                       class="flex-1 border border-gray-300 text-gray-700 text-center py-3 rounded-xl font-semibold hover:bg-gray-50 transition-colors">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

<script>
    // Preview profile picture before upload
    document.getElementById('profile_picture').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.querySelector('img[alt="Current Profile Picture"]') || 
                           document.querySelector('.w-20.h-20.rounded-full');
                if (img.tagName === 'IMG') {
                    img.src = e.target.result;
                } else {
                    // Replace the div with an img
                    const newImg = document.createElement('img');
                    newImg.src = e.target.result;
                    newImg.alt = 'Profile Preview';
                    newImg.className = 'w-20 h-20 rounded-full object-cover border-2 border-emerald-200';
                    img.parentNode.replaceChild(newImg, img);
                }
            };
            reader.readAsDataURL(file);
        }
    });
</script>
{% endblock %}
