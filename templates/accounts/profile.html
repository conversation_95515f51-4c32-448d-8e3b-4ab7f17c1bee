{% extends 'base.html' %}

{% block title %}Profile - AkriOnline{% endblock %}

{% block content %}
<section class="py-20 bg-gradient-to-br from-gray-50 via-white to-emerald-50 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12 reveal">
            <h1 class="font-display font-bold text-4xl lg:text-5xl text-gray-900 mb-4">
                My <span class="gradient-primary bg-clip-text text-transparent">Profile</span>
            </h1>
            <p class="text-lg text-gray-600">
                Manage your account information and preferences
            </p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Profile Card -->
            <div class="lg:col-span-1">
                <div class="glass p-8 rounded-3xl border-2 border-white/20 hover-lift">
                    <!-- Profile Picture -->
                    <div class="text-center mb-6">
                        {% if user.profile_picture %}
                            <img src="{{ user.profile_picture.url }}" alt="Profile Picture" 
                                 class="w-32 h-32 rounded-full mx-auto mb-4 object-cover border-4 border-emerald-200">
                        {% else %}
                            <div class="w-32 h-32 rounded-full mx-auto mb-4 gradient-primary flex items-center justify-center">
                                <span class="text-4xl font-bold text-white">
                                    {{ user.first_name|first|default:user.username|first|upper }}
                                </span>
                            </div>
                        {% endif %}
                        <h2 class="font-display font-bold text-2xl text-gray-900">
                            {{ user.first_name }} {{ user.last_name|default:"" }}
                        </h2>
                        <p class="text-gray-600">{{ user.get_user_type_display }}</p>
                        {% if user.user_type == 'dealer' and is_dealer %}
                            <span class="inline-block mt-2 px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium">
                                {% if user.dealer_profile.is_verified %}
                                    ✓ Verified Dealer
                                {% else %}
                                    Pending Verification
                                {% endif %}
                            </span>
                        {% endif %}
                    </div>

                    <!-- Quick Stats -->
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-white/50 rounded-xl">
                            <span class="text-gray-600">Eco Points</span>
                            <span class="font-bold text-emerald-600">{{ user.eco_points }}</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-white/50 rounded-xl">
                            <span class="text-gray-600">Member Since</span>
                            <span class="font-medium text-gray-900">{{ user.date_joined|date:"M Y" }}</span>
                        </div>
                        {% if user.user_type == 'dealer' and is_dealer %}
                            <div class="flex justify-between items-center p-3 bg-white/50 rounded-xl">
                                <span class="text-gray-600">Rating</span>
                                <span class="font-medium text-gray-900">
                                    {{ user.dealer_profile.average_rating|floatformat:1 }}/5.0
                                </span>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-6 space-y-3">
                        <a href="{% url 'accounts:edit_profile' %}" 
                           class="w-full btn-primary text-white text-center py-3 rounded-xl font-semibold block">
                            Edit Profile
                        </a>
                        {% if user.user_type == 'dealer' and is_dealer %}
                            <a href="{% url 'accounts:dealer_dashboard' %}" 
                               class="w-full border border-emerald-600 text-emerald-600 text-center py-3 rounded-xl font-semibold block hover:bg-emerald-50 transition-colors">
                                Dealer Dashboard
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Profile Details -->
            <div class="lg:col-span-2">
                <div class="glass p-8 rounded-3xl border-2 border-white/20">
                    <h3 class="font-display font-bold text-2xl text-gray-900 mb-6">Account Information</h3>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <div class="p-3 bg-gray-50 rounded-xl text-gray-900">{{ user.username }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <div class="p-3 bg-gray-50 rounded-xl text-gray-900">{{ user.email }}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <div class="p-3 bg-gray-50 rounded-xl text-gray-900">
                                {{ user.phone_number|default:"Not provided" }}
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                            <div class="p-3 bg-gray-50 rounded-xl text-gray-900">
                                {{ user.city|default:"Not provided" }}
                            </div>
                        </div>
                    </div>

                    {% if user.address %}
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                            <div class="p-3 bg-gray-50 rounded-xl text-gray-900">{{ user.address }}</div>
                        </div>
                    {% endif %}

                    {% if user.user_type == 'dealer' and is_dealer %}
                        <div class="mt-8 pt-8 border-t border-gray-200">
                            <h4 class="font-display font-bold text-xl text-gray-900 mb-4">Dealer Information</h4>
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
                                    <div class="p-3 bg-gray-50 rounded-xl text-gray-900">
                                        {{ user.dealer_profile.business_name }}
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Verification Status</label>
                                    <div class="p-3 bg-gray-50 rounded-xl">
                                        <span class="px-3 py-1 rounded-full text-sm font-medium
                                            {% if user.dealer_profile.verification_status == 'verified' %}
                                                bg-green-100 text-green-800
                                            {% elif user.dealer_profile.verification_status == 'pending' %}
                                                bg-yellow-100 text-yellow-800
                                            {% else %}
                                                bg-red-100 text-red-800
                                            {% endif %}">
                                            {{ user.dealer_profile.get_verification_status_display }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
