{% extends 'base.html' %}

{% block title %}Sign Up - AkriOnline{% endblock %}

{% block content %}
<section class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 via-white to-blue-50">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 right-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div class="absolute bottom-1/4 left-10 w-80 h-80 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style="animation-delay: 2s;"></div>
    </div>

    <div class="relative z-10 max-w-md w-full space-y-8">
        <div class="text-center">
            <!-- Logo -->
            <div class="flex justify-center mb-6">
                <div class="w-16 h-16 rounded-3xl gradient-primary flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                    </svg>
                </div>
            </div>
            
            <h2 class="font-display font-bold text-3xl text-gray-900 mb-2">Join AkriOnline</h2>
            <p class="text-gray-600">Create your sustainable trading account</p>
        </div>

        <!-- Social Login -->
        <div class="space-y-3">
            <a href="{% url 'accounts:google_login' %}" class="w-full flex justify-center items-center px-4 py-3 border border-gray-300 rounded-xl shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-300 hover-lift">
                <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Continue with Google
            </a>
            
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white text-gray-500">Or register with email</span>
                </div>
            </div>
        </div>

        <!-- Registration Form -->
        <div class="glass p-8 rounded-3xl border-2 border-white/20 hover-lift">
            {% if messages %}
                {% for message in messages %}
                    <div class="mb-4 p-4 rounded-xl {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-emerald-100 text-emerald-700{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- User Type Selection -->
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-3">Account Type</label>
                    <div class="grid grid-cols-2 gap-3">
                        <label class="relative">
                            <input type="radio" name="user_type" value="regular" checked class="sr-only peer">
                            <div class="p-4 rounded-xl border-2 border-gray-200 peer-checked:border-emerald-500 peer-checked:bg-emerald-50 cursor-pointer transition-all hover:border-emerald-300">
                                <div class="text-center">
                                    <div class="text-2xl mb-2">👤</div>
                                    <div class="font-semibold text-gray-900">Regular User</div>
                                    <div class="text-xs text-gray-600">Buy/Sell materials</div>
                                </div>
                            </div>
                        </label>
                        <label class="relative">
                            <input type="radio" name="user_type" value="dealer" class="sr-only peer">
                            <div class="p-4 rounded-xl border-2 border-gray-200 peer-checked:border-blue-500 peer-checked:bg-blue-50 cursor-pointer transition-all hover:border-blue-300">
                                <div class="text-center">
                                    <div class="text-2xl mb-2">🏢</div>
                                    <div class="font-semibold text-gray-900">Scrap Dealer</div>
                                    <div class="text-xs text-gray-600">Business account</div>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-semibold text-gray-700 mb-2">First Name</label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.first_name.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-semibold text-gray-700 mb-2">Last Name</label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.last_name.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-semibold text-gray-700 mb-2">Username</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-semibold text-gray-700 mb-2">Email</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                    {% endif %}
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-semibold text-gray-700 mb-2">Phone</label>
                        {{ form.phone_number }}
                        {% if form.phone_number.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.phone_number.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.city.id_for_label }}" class="block text-sm font-semibold text-gray-700 mb-2">City</label>
                        {{ form.city }}
                        {% if form.city.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.city.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-semibold text-gray-700 mb-2">Password</label>
                    {{ form.password1 }}
                    {% if form.password1.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.password1.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-semibold text-gray-700 mb-2">Confirm Password</label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.password2.errors.0 }}</p>
                    {% endif %}
                </div>

                <div class="flex items-center">
                    <input id="terms" name="terms" type="checkbox" class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded" required>
                    <label for="terms" class="ml-2 block text-sm text-gray-700">
                        I agree to the 
                        <a href="#" class="text-emerald-600 hover:text-emerald-500 font-medium">Terms of Service</a> 
                        and 
                        <a href="#" class="text-emerald-600 hover:text-emerald-500 font-medium">Privacy Policy</a>
                    </label>
                </div>

                <button type="submit" class="w-full btn-primary text-white py-3 px-4 rounded-xl font-semibold hover-lift">
                    Create Account
                </button>
            </form>

            <div class="mt-6 text-center">
                <p class="text-gray-600">
                    Already have an account?
                    <a href="{% url 'accounts:login' %}" class="font-semibold text-emerald-600 hover:text-emerald-500 transition-colors">
                        Sign in here
                    </a>
                </p>
            </div>
        </div>

        <!-- Benefits -->
        <div class="text-center space-y-4">
            <p class="text-gray-600 font-medium">Join 15,000+ users who are already:</p>
            <div class="flex justify-center space-x-8 text-sm text-gray-500">
                <div class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span>Trading Sustainably</span>
                </div>
                <div class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Earning Eco Points</span>
                </div>
                <div class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>Saving the Planet</span>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    /* Style form inputs */
    input[type="text"], input[type="email"], input[type="password"] {
        @apply w-full px-4 py-3 border border-gray-200 rounded-xl bg-white/50 backdrop-blur-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300;
    }
    
    input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus {
        @apply bg-white transform scale-105;
    }
</style>
{% endblock %}