{% extends 'base.html' %}

{% block title %}Verified Dealers Directory - AkriOnline{% endblock %}

{% block content %}
<section class="py-20 bg-gradient-to-br from-gray-50 via-white to-emerald-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12 reveal">
            <h1 class="font-display font-bold text-5xl lg:text-6xl text-gray-900 mb-6">
                Verified <span class="gradient-primary bg-clip-text text-transparent">Dealers</span>
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Connect with trusted scrap dealers across India. Get the best prices for your materials.
            </p>
        </div>

        <!-- Search and Filters -->
        <div class="glass p-6 rounded-3xl border-2 border-white/20 mb-8 reveal">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <input type="text" name="search" value="{{ search }}" placeholder="Search dealers..." 
                           class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                </div>
                <div>
                    <select name="category" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.name }}" {% if selected_category == category.name %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <select name="city" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">All Cities</option>
                        {% for city in cities %}
                            <option value="{{ city }}" {% if selected_city == city %}selected{% endif %}>
                                {{ city }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <button type="submit" class="w-full btn-primary text-white py-3 px-4 rounded-xl font-semibold hover-lift">
                        Search
                    </button>
                </div>
            </form>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 reveal">
            <div class="glass p-6 rounded-2xl text-center hover-lift">
                <div class="text-2xl font-bold text-emerald-600 mb-2">{{ dealers.paginator.count }}+</div>
                <div class="text-gray-600">Verified Dealers</div>
            </div>
            <div class="glass p-6 rounded-2xl text-center hover-lift">
                <div class="text-2xl font-bold text-blue-600 mb-2">{{ categories.count }}+</div>
                <div class="text-gray-600">Material Types</div>
            </div>
            <div class="glass p-6 rounded-2xl text-center hover-lift">
                <div class="text-2xl font-bold text-purple-600 mb-2">{{ cities|length }}+</div>
                <div class="text-gray-600">Cities Covered</div>
            </div>
            <div class="glass p-6 rounded-2xl text-center hover-lift">
                <div class="text-2xl font-bold text-orange-600 mb-2">24/7</div>
                <div class="text-gray-600">Platform Support</div>
            </div>
        </div>

        <!-- Dealers Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {% for dealer in dealers %}
                <div class="glass p-6 rounded-3xl border-2 border-white/20 hover-lift reveal">
                    <!-- Dealer Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="font-display font-bold text-xl text-gray-900 mb-2">{{ dealer.business_name }}</h3>
                            <div class="flex items-center space-x-2 mb-2">
                                <svg class="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-sm text-emerald-600 font-semibold">Verified Dealer</span>
                            </div>
                            <div class="flex items-center space-x-1 text-sm text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                <span>{{ dealer.user.city }}</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center space-x-1">
                                {% for i in "12345" %}
                                    <svg class="w-4 h-4 {% if dealer.average_rating >= forloop.counter %}text-yellow-400{% else %}text-gray-300{% endif %}" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                {% endfor %}
                            </div>
                            <div class="text-sm text-gray-600">{{ dealer.average_rating }}/5</div>
                        </div>
                    </div>

                    <!-- Specialization -->
                    <div class="mb-4">
                        <p class="text-gray-600 text-sm line-clamp-2">{{ dealer.specialization|truncatewords:15 }}</p>
                    </div>

                    <!-- Services -->
                    <div class="flex flex-wrap gap-2 mb-4">
                        {% if dealer.pickup_available %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                                🚚 Pickup Available
                            </span>
                        {% endif %}
                        {% if dealer.delivery_available %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                📦 Delivery
                            </span>
                        {% endif %}
                    </div>

                    <!-- Stats -->
                    <div class="grid grid-cols-2 gap-4 mb-4 p-3 bg-gray-50 rounded-xl">
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900">{{ dealer.total_transactions }}</div>
                            <div class="text-xs text-gray-600">Transactions</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900">{{ dealer.years_in_business|default:"N/A" }}</div>
                            <div class="text-xs text-gray-600">Years Experience</div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex space-x-3">
                        <a href="{% url 'accounts:dealer_detail' dealer.id %}" 
                           class="flex-1 bg-emerald-600 text-white text-center py-2 px-4 rounded-xl font-semibold hover:bg-emerald-700 transition-colors">
                            View Prices
                        </a>
                        <a href="{% url 'accounts:dealer_detail' dealer.id %}#contact" 
                           class="flex-1 border border-emerald-600 text-emerald-600 text-center py-2 px-4 rounded-xl font-semibold hover:bg-emerald-50 transition-colors">
                            Contact
                        </a>
                    </div>
                </div>
            {% empty %}
                <div class="col-span-full text-center py-12">
                    <div class="text-6xl mb-4">🔍</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No dealers found</h3>
                    <p class="text-gray-600">Try adjusting your search criteria</p>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if dealers.has_other_pages %}
            <div class="flex justify-center space-x-2">
                {% if dealers.has_previous %}
                    <a href="?page={{ dealers.previous_page_number }}&search={{ search }}&category={{ selected_category }}&city={{ selected_city }}" 
                       class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">Previous</a>
                {% endif %}

                {% for num in dealers.paginator.page_range %}
                    {% if num == dealers.number %}
                        <span class="px-4 py-2 bg-emerald-600 text-white rounded-lg">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}&search={{ search }}&category={{ selected_category }}&city={{ selected_city }}" 
                           class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}

                {% if dealers.has_next %}
                    <a href="?page={{ dealers.next_page_number }}&search={{ search }}&category={{ selected_category }}&city={{ selected_city }}" 
                       class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">Next</a>
                {% endif %}
            </div>
        {% endif %}

        <!-- CTA Section -->
        <div class="text-center mt-16 glass p-8 rounded-3xl border-2 border-white/20 reveal">
            <h3 class="font-display font-bold text-2xl text-gray-900 mb-4">
                Want to become a verified dealer?
            </h3>
            <p class="text-gray-600 mb-6">
                Join our platform and connect with thousands of potential customers
            </p>
            <a href="{% url 'accounts:register' %}" 
               class="btn-primary text-white px-8 py-3 rounded-xl font-semibold hover-lift">
                Register as Dealer
            </a>
        </div>
    </div>
</section>

<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
{% endblock %}