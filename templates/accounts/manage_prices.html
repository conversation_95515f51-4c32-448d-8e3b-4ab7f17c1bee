{% extends 'base.html' %}

{% block title %}Manage Prices - AkriOnline{% endblock %}

{% block content %}
<section class="py-20 bg-gradient-to-br from-gray-50 via-white to-emerald-50 min-h-screen">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-12 reveal">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="font-display font-bold text-4xl lg:text-5xl text-gray-900 mb-2">
                        Manage <span class="gradient-primary bg-clip-text text-transparent">Prices</span>
                    </h1>
                    <p class="text-lg text-gray-600">
                        Set competitive prices for your scrap materials
                    </p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="{% url 'accounts:dealer_dashboard' %}" 
                       class="border border-gray-300 text-gray-700 px-6 py-2 rounded-xl font-semibold hover:bg-gray-50 transition-colors">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="glass p-6 rounded-3xl border-2 border-blue-100 mb-8">
            <h3 class="font-display font-bold text-xl text-blue-900 mb-4">💡 Pricing Guidelines</h3>
            <div class="grid md:grid-cols-2 gap-4 text-blue-800">
                <ul class="space-y-2">
                    <li>• Set competitive prices based on current market rates</li>
                    <li>• Different grades have different price points</li>
                    <li>• Consider your minimum quantity requirements</li>
                </ul>
                <ul class="space-y-2">
                    <li>• Prices are displayed per unit (kg, ton, piece)</li>
                    <li>• You can deactivate materials you don't deal with</li>
                    <li>• Regular price updates help maintain competitiveness</li>
                </ul>
            </div>
        </div>

        <div class="glass p-8 rounded-3xl border-2 border-white/20">
            <form method="post" class="space-y-8">
                {% csrf_token %}
                
                <!-- Display Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="p-4 rounded-xl {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Form Management -->
                {{ formset.management_form }}

                <!-- Material Categories -->
                {% for category in categories %}
                    <div class="border border-gray-200 rounded-2xl p-6">
                        <h3 class="font-display font-bold text-2xl text-gray-900 mb-6 flex items-center">
                            {% if category.icon %}
                                <span class="mr-3 text-2xl">{{ category.icon }}</span>
                            {% endif %}
                            {{ category.name }}
                        </h3>
                        
                        <div class="space-y-4">
                            {% for material in category.materials.all %}
                                <div class="bg-white/50 p-4 rounded-xl">
                                    <h4 class="font-semibold text-lg text-gray-900 mb-4">{{ material.name }}</h4>
                                    <p class="text-gray-600 text-sm mb-4">{{ material.description }}</p>
                                    
                                    <!-- Price forms for this material -->
                                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                                        {% for form in formset %}
                                            {% if form.instance.material == material or not form.instance.pk %}
                                                <div class="border border-gray-200 rounded-lg p-4">
                                                    {% for hidden in form.hidden_fields %}
                                                        {{ hidden }}
                                                    {% endfor %}
                                                    
                                                    <div class="space-y-3">
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">Material</label>
                                                            {{ form.material }}
                                                        </div>
                                                        
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">Quality Grade</label>
                                                            {{ form.quality_grade }}
                                                        </div>
                                                        
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                                                Price per {{ material.unit }}
                                                            </label>
                                                            {{ form.price_per_unit }}
                                                        </div>
                                                        
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                                                Min Quantity ({{ material.unit }})
                                                            </label>
                                                            {{ form.minimum_quantity }}
                                                        </div>
                                                        
                                                        <div class="flex items-center space-x-2">
                                                            {{ form.is_active }}
                                                            <label for="{{ form.is_active.id_for_label }}" class="text-sm text-gray-700">
                                                                Active
                                                            </label>
                                                        </div>
                                                        
                                                        {% if form.instance.pk %}
                                                            <div class="flex items-center space-x-2">
                                                                {{ form.DELETE }}
                                                                <label for="{{ form.DELETE.id_for_label }}" class="text-sm text-red-600">
                                                                    Delete
                                                                </label>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% empty %}
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">📦</div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No material categories found</h3>
                        <p class="text-gray-600">Contact admin to add material categories</p>
                    </div>
                {% endfor %}

                <!-- Form Errors -->
                {% if formset.errors %}
                    <div class="p-4 bg-red-100 text-red-800 rounded-xl">
                        <h4 class="font-semibold mb-2">Please correct the following errors:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            {% for form in formset %}
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>Form {{ forloop.parentloop.counter }}: {{ field|title }} - {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <!-- Submit Button -->
                <div class="flex space-x-4 pt-6 border-t border-gray-200">
                    <button type="submit" 
                            class="flex-1 btn-primary text-white py-4 rounded-xl font-semibold text-lg hover-lift">
                        Save Prices
                    </button>
                    <a href="{% url 'accounts:dealer_dashboard' %}" 
                       class="flex-1 border border-gray-300 text-gray-700 text-center py-4 rounded-xl font-semibold hover:bg-gray-50 transition-colors">
                        Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Market Insights -->
        <div class="mt-8 glass p-6 rounded-3xl border-2 border-white/20">
            <h3 class="font-display font-bold text-xl text-gray-900 mb-4">💡 Market Insights</h3>
            <div class="grid md:grid-cols-3 gap-4 text-sm">
                <div class="p-4 bg-green-50 rounded-xl">
                    <h4 class="font-semibold text-green-900 mb-2">Competitive Pricing</h4>
                    <p class="text-green-800">Check competitor prices regularly to stay competitive in the market.</p>
                </div>
                <div class="p-4 bg-blue-50 rounded-xl">
                    <h4 class="font-semibold text-blue-900 mb-2">Quality Matters</h4>
                    <p class="text-blue-800">Higher quality grades typically command premium prices.</p>
                </div>
                <div class="p-4 bg-purple-50 rounded-xl">
                    <h4 class="font-semibold text-purple-900 mb-2">Volume Discounts</h4>
                    <p class="text-purple-800">Consider offering better rates for larger quantities.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Add dynamic form functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-select material when in material context
        const materialSelects = document.querySelectorAll('select[name$="-material"]');
        materialSelects.forEach(select => {
            if (!select.value) {
                // Auto-select based on context if needed
                // This would require additional JavaScript logic
            }
        });
        
        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            let hasValidPrice = false;
            const priceInputs = document.querySelectorAll('input[name$="-price_per_unit"]');
            
            priceInputs.forEach(input => {
                if (input.value && parseFloat(input.value) > 0) {
                    hasValidPrice = true;
                }
            });
            
            if (!hasValidPrice) {
                e.preventDefault();
                alert('Please set at least one price for your materials.');
            }
        });
    });
</script>
{% endblock %}
