{% extends 'base.html' %}

{% block title %}{{ dealer.business_name }} - Verified Dealer - AkriOnline{% endblock %}

{% block content %}
<section class="py-20 bg-gradient-to-br from-gray-50 via-white to-emerald-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Dealer Header -->
        <div class="glass p-8 rounded-3xl border-2 border-white/20 mb-8 reveal">
            <div class="grid lg:grid-cols-3 gap-8">
                <!-- Business Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-start justify-between mb-6">
                        <div>
                            <h1 class="font-display font-bold text-4xl text-gray-900 mb-2">{{ dealer.business_name }}</h1>
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    <span class="text-emerald-600 font-semibold">Verified Dealer</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    {% for i in "12345" %}
                                        <svg class="w-5 h-5 {% if dealer.average_rating >= forloop.counter %}text-yellow-400{% else %}text-gray-300{% endif %}" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    {% endfor %}
                                    <span class="ml-2 text-gray-600">{{ dealer.average_rating }}/5 ({{ dealer.total_ratings }} reviews)</span>
                                </div>
                            </div>
                            
                            <div class="space-y-2 text-gray-600">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    <span>{{ dealer.business_address }}</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    <span>{{ dealer.business_phone }}</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                    </svg>
                                    <span>{{ dealer.business_email }}</span>
                                </div>
                                {% if dealer.operating_hours %}
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span>{{ dealer.operating_hours }}</span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Specialization -->
                    <div class="mb-6">
                        <h3 class="font-semibold text-lg text-gray-900 mb-2">Specialization</h3>
                        <p class="text-gray-600">{{ dealer.specialization }}</p>
                    </div>

                    <!-- Services -->
                    <div class="flex flex-wrap gap-3">
                        {% if dealer.pickup_available %}
                            <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-emerald-100 text-emerald-800">
                                🚚 Pickup Available
                            </span>
                        {% endif %}
                        {% if dealer.delivery_available %}
                            <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                📦 Delivery Service
                            </span>
                        {% endif %}
                        {% if dealer.minimum_quantity %}
                            <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                📏 Min: {{ dealer.minimum_quantity }}
                            </span>
                        {% endif %}
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="space-y-4">
                    <div class="bg-gradient-to-r from-emerald-500 to-blue-600 p-6 rounded-2xl text-white text-center">
                        <div class="text-3xl font-bold">{{ dealer.total_transactions }}</div>
                        <div class="text-emerald-100">Successful Transactions</div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-pink-600 p-6 rounded-2xl text-white text-center">
                        <div class="text-3xl font-bold">{{ dealer.years_in_business|default:"5+" }}</div>
                        <div class="text-purple-100">Years in Business</div>
                    </div>
                    {% if can_contact %}
                        <button onclick="openContactModal()" class="w-full btn-primary text-white py-3 px-4 rounded-xl font-semibold hover-lift">
                            Contact Dealer
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Price Tables -->
        <div class="mb-8">
            <h2 class="font-display font-bold text-3xl text-gray-900 mb-6 reveal">Current Prices</h2>
            
            {% for category, prices in prices_by_category.items %}
                <div class="glass p-6 rounded-3xl border-2 border-white/20 mb-6 reveal">
                    <h3 class="font-semibold text-xl text-gray-900 mb-4 flex items-center">
                        <span class="text-2xl mr-3">
                            {% if category == "Plastic" %}♻️
                            {% elif category == "Metal" %}🔩
                            {% elif category == "Paper" %}📄
                            {% elif category == "E-Waste" %}💻
                            {% elif category == "Glass" %}🍾
                            {% elif category == "Textile" %}👕
                            {% else %}📦{% endif %}
                        </span>
                        {{ category }}
                    </h3>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 font-semibold text-gray-900">Material</th>
                                    <th class="text-center py-3 px-4 font-semibold text-gray-900">Grade</th>
                                    <th class="text-right py-3 px-4 font-semibold text-gray-900">Price</th>
                                    <th class="text-right py-3 px-4 font-semibold text-gray-900">Min Quantity</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for price in prices %}
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-3 px-4 text-gray-900">{{ price.material.name }}</td>
                                        <td class="py-3 px-4 text-center">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                                {% if price.quality_grade == 'A' %}bg-green-100 text-green-800
                                                {% elif price.quality_grade == 'B' %}bg-blue-100 text-blue-800
                                                {% elif price.quality_grade == 'C' %}bg-yellow-100 text-yellow-800
                                                {% else %}bg-red-100 text-red-800{% endif %}">
                                                Grade {{ price.quality_grade }}
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 text-right font-semibold text-emerald-600">
                                            ₹{{ price.price_per_unit }}/{{ price.material.unit }}
                                        </td>
                                        <td class="py-3 px-4 text-right text-gray-600">
                                            {{ price.minimum_quantity }} {{ price.material.unit }}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% empty %}
                <div class="glass p-8 rounded-3xl border-2 border-white/20 text-center">
                    <div class="text-6xl mb-4">📋</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No prices available</h3>
                    <p class="text-gray-600">This dealer hasn't updated their prices yet.</p>
                </div>
            {% endfor %}
        </div>

        <!-- Reviews Section -->
        <div id="reviews" class="mb-8">
            <h2 class="font-display font-bold text-3xl text-gray-900 mb-6 reveal">Customer Reviews</h2>
            
            <div class="grid md:grid-cols-2 gap-6">
                {% for rating in ratings %}
                    <div class="glass p-6 rounded-2xl border-2 border-white/20 reveal">
                        <div class="flex items-start justify-between mb-3">
                            <div>
                                <div class="font-semibold text-gray-900">{{ rating.user.first_name|default:rating.user.username }}</div>
                                <div class="flex items-center space-x-1">
                                    {% for i in "12345" %}
                                        <svg class="w-4 h-4 {% if rating.rating >= forloop.counter %}text-yellow-400{% else %}text-gray-300{% endif %}" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="text-sm text-gray-500">{{ rating.created_at|date:"M d, Y" }}</div>
                        </div>
                        {% if rating.review %}
                            <p class="text-gray-600">{{ rating.review }}</p>
                        {% endif %}
                    </div>
                {% empty %}
                    <div class="col-span-2 glass p-8 rounded-3xl border-2 border-white/20 text-center">
                        <div class="text-6xl mb-4">💬</div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No reviews yet</h3>
                        <p class="text-gray-600">Be the first to review this dealer!</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</section>

<!-- Contact Modal -->
{% if can_contact %}
<div id="contactModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="glass max-w-md w-full p-6 rounded-3xl border-2 border-white/20">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-gray-900">Contact {{ dealer.business_name }}</h3>
                <button onclick="closeContactModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <form id="contactForm" onsubmit="submitContact(event)">
                {% csrf_token %}
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">Subject</label>
                        <input type="text" name="subject" required class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">Material (Optional)</label>
                        <select name="material" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                            <option value="">Select material</option>
                            {% for category, prices in prices_by_category.items %}
                                {% for price in prices %}
                                    <option value="{{ price.material.id }}">{{ price.material.name }}</option>
                                {% endfor %}
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">Quantity (Optional)</label>
                        <input type="text" name="quantity" placeholder="e.g., 100kg" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">Message</label>
                        <textarea name="message" rows="4" required class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">Contact Preference</label>
                        <select name="contact_preference" class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                            <option value="email">Email</option>
                            <option value="phone">Phone</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="w-full btn-primary text-white py-3 px-4 rounded-xl font-semibold hover-lift">
                        Send Message
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<script>
function openContactModal() {
    document.getElementById('contactModal').classList.remove('hidden');
}

function closeContactModal() {
    document.getElementById('contactModal').classList.add('hidden');
}

function submitContact(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    
    fetch('{% url "accounts:contact_dealer" dealer.id %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeContactModal();
            // Show success message
            alert('Your message has been sent successfully!');
        } else {
            alert('Error sending message. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error sending message. Please try again.');
    });
}
</script>
{% endblock %}