<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AkriOnline - Sustainable Marketplace{% endblock %}</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js" rel="stylesheet">
    
    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <style>
        /* Custom Tailwind Configuration */
        :root {
            --primary-green: #10b981;
            --primary-green-light: #34d399;
            --primary-green-dark: #047857;
            --accent-blue: #3b82f6;
            --gradient-primary: linear-gradient(135deg, #10b981, #3b82f6);
            --gradient-secondary: linear-gradient(135deg, #f3ec78, #af4261);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
        }
        
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .font-display {
            font-family: 'Space Grotesk', sans-serif;
        }
        
        /* Glassmorphism Effect */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
        }
        
        /* Animated Gradients */
        .gradient-primary {
            background: linear-gradient(135deg, #10b981, #3b82f6, #8b5cf6);
            background-size: 200% 200%;
            animation: gradientShift 6s ease infinite;
        }
        
        .gradient-secondary {
            background: linear-gradient(135deg, #f3ec78, #af4261, #ff6b6b);
            background-size: 200% 200%;
            animation: gradientShift 8s ease infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* Floating Animation */
        .float {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }
        
        /* Pulse Animation */
        .pulse-slow {
            animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        /* Hover Effects */
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        /* Text Glow */
        .text-glow {
            text-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
        }
        
        /* Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(16, 185, 129, 0.4);
        }
        
        /* Particle Animation */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(16, 185, 129, 0.5);
            border-radius: 50%;
            animation: particleFloat 10s linear infinite;
        }
        
        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) translateX(100px);
                opacity: 0;
            }
        }
        
        /* Scroll Animations */
        .reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .reveal.active {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #047857, #1d4ed8);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gradient-to-br from-slate-50 via-white to-green-50 min-h-screen overflow-x-hidden">
    <!-- Particle Background -->
    <div class="particles fixed inset-0 pointer-events-none z-0">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 40%; animation-delay: 6s;"></div>
        <div class="particle" style="left: 50%; animation-delay: 8s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 7s;"></div>
    </div>

    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 glass border-0 border-b border-white/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 rounded-2xl gradient-primary flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                        </svg>
                    </div>
                    <span class="font-display font-bold text-2xl text-gray-900">Akri<span class="text-emerald-600">Online</span></span>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{% url 'home:home' %}" class="text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300 hover:scale-105">Home</a>
                    <a href="#marketplace" class="text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300 hover:scale-105">Marketplace</a>
                    <a href="#how-it-works" class="text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300 hover:scale-105">How It Works</a>
                    <a href="#about" class="text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300 hover:scale-105">About</a>
                </div>

                <!-- Auth Buttons -->
                <div class="flex items-center space-x-4">
                    {% if user.is_authenticated %}
                        <div class="flex items-center space-x-3">
                            <span class="text-gray-700 font-medium">Welcome, {{ user.first_name|default:user.username }}</span>
                            <a href="{% url 'accounts:logout' %}" class="text-gray-600 hover:text-red-600 transition-colors">Logout</a>
                        </div>
                    {% else %}
                        <a href="{% url 'accounts:login' %}" class="text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300">Login</a>
                        <a href="{% url 'accounts:register' %}" class="btn-primary text-white px-6 py-2 rounded-full font-medium">Sign Up</a>
                    {% endif %}
                </div>

                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button class="text-gray-700 hover:text-emerald-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="relative z-10 pt-16">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white relative z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded-lg gradient-primary flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                            </svg>
                        </div>
                        <span class="font-display font-bold text-xl">AkriOnline</span>
                    </div>
                    <p class="text-gray-400">India's premier sustainable marketplace for scrap trading and reusable items.</p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-lg mb-4">Marketplace</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-emerald-400 transition-colors">Scrap Materials</a></li>
                        <li><a href="#" class="hover:text-emerald-400 transition-colors">Reusable Items</a></li>
                        <li><a href="#" class="hover:text-emerald-400 transition-colors">Electronics</a></li>
                        <li><a href="#" class="hover:text-emerald-400 transition-colors">Furniture</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold text-lg mb-4">Company</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-emerald-400 transition-colors">About Us</a></li>
                        <li><a href="#" class="hover:text-emerald-400 transition-colors">Contact</a></li>
                        <li><a href="#" class="hover:text-emerald-400 transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-emerald-400 transition-colors">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold text-lg mb-4">Connect</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 rounded-full bg-emerald-600 flex items-center justify-center hover:bg-emerald-700 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-emerald-600 flex items-center justify-center hover:bg-emerald-700 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 AkriOnline. All rights reserved. | Built with 💚 for sustainability</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Animations -->
    <script>
        // Reveal animations on scroll
        const observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('active');
                }
            });
        }, observerOptions);

        document.addEventListener('DOMContentLoaded', () => {
            // Observe all elements with reveal class
            document.querySelectorAll('.reveal').forEach(el => {
                observer.observe(el);
            });

            // Add smooth hover effects
            document.querySelectorAll('.hover-lift').forEach(el => {
                el.addEventListener('mouseenter', () => {
                    el.style.transform = 'translateY(-8px) scale(1.02)';
                });
                
                el.addEventListener('mouseleave', () => {
                    el.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // Mobile menu toggle (basic implementation)
        // You can expand this as needed
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>